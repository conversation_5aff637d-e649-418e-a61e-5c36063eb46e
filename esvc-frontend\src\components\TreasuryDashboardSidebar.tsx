import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

// Import icons
import overviewIcon from '../assets/overview.png';
import walletMoneyIcon from '../assets/wallet-money.png';
import arrow2Icon from '../assets/arrow-2.png';
import cardCoinIcon from '../assets/card-coin.png';
import moneysIcon from '../assets/moneys.png';
import percentageSquareIcon from '../assets/percentage-square.png';
import chartIcon from '../assets/chart.png';

interface TreasuryDashboardSidebarProps {
  className?: string;
}

const TreasuryDashboardSidebar: React.FC<TreasuryDashboardSidebarProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: overviewIcon, path: '/overview' },
    { id: 'live-reserve', label: 'Live Reserve', icon: walletMoneyIcon, path: '/live-reserve' },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: arrow2Icon, path: '/daily-transactions' },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: cardCoinIcon, path: '/real-time-staking' },
    { id: 'startup-funding', label: 'Startup Funding', icon: moneysIcon, path: '/startup-funding' },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: percentageSquareIcon, path: '/roi-distribution' },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: chartIcon, path: '/visual-analytics' }
  ];

  const handleSidebarClick = (path: string) => {
    navigate(path);
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <aside className={`dashboard-sidebar ${className}`}>
      <nav className="sidebar-nav">
        {sidebarItems.map((item) => (
          <button
            key={item.id}
            className={`sidebar-item ${isActive(item.path) ? 'active' : ''}`}
            onClick={() => handleSidebarClick(item.path)}
          >
            <img src={item.icon} alt={item.label} className="sidebar-icon" />
            <span className="sidebar-label">{item.label}</span>
          </button>
        ))}
      </nav>
    </aside>
  );
};

export default TreasuryDashboardSidebar;

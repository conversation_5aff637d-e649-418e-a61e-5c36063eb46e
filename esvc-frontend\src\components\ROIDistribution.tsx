import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts';
import '../styles/components/ROIDistribution.css';
import DashboardLayout from './DashboardLayout';
import TreasuryDashboardSidebar from './TreasuryDashboardSidebar';
import TreasuryDashboardHeader from './TreasuryDashboardHeader';

// Import icons
import trendsUpIcon from '../assets/trends-up.png';

interface ROIDistributionProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const ROIDistribution: React.FC<ROIDistributionProps> = () => {
  const [timeFilter, setTimeFilter] = useState('this-year');
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);



  const timeFilters = [
    { id: 'this-year', label: 'This year' },
    { id: 'last-year', label: 'Last year' },
    { id: 'all-time', label: 'All time' }
  ];

  const totalPaidOut = '$243,700';
  const averageDailyPayout = '$24,500';
  const changePercent = '+0.8% Today';

  // Mock chart data for monthly ROI payouts
  const chartData = [
    { month: 'Jan', value: 750000 },
    { month: 'Feb', value: 800000 },
    { month: 'Mar', value: 850000 },
    { month: 'Apr', value: 900000 },
    { month: 'May', value: 950000 },
    { month: 'Jun', value: 1000000 },
    { month: 'Jul', value: 1050000 },
    { month: 'Aug', value: 1100000 }
  ];



  const formatValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    }
    return `$${(value / 1000).toFixed(0)}K`;
  };

  const formatYAxisTick = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    }
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`;
    }
    return `$${value}`;
  };

  return (
    <DashboardLayout className="roi-distribution-container">
      <div className="roi-distribution-content">
        {/* Page Title */}
        <TreasuryDashboardHeader />

        <div className="dashboard-layout">
          {/* Sidebar */}
          <TreasuryDashboardSidebar />

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="roi-header">
              <h2 className="section-title">ROI Distribution</h2>
            </div>

            {/* Stats Cards */}
            <div className="roi-stats">
              <div className="stats-card">
                <div className="stats-label">TOTAL ROI PAID OUT (TO DATE)</div>
                <div className="stats-value">{totalPaidOut}</div>
                <div className="stats-change">
                  <img src={trendUpIcon} alt="Trend up" className="trend-icon" />
                  {changePercent}
                </div>
              </div>

              <div className="stats-card">
                <div className="stats-label">AVERAGE DAILY PAYOUT</div>
                <div className="stats-value">{averageDailyPayout}</div>
              </div>
            </div>

            {/* Chart Section */}
            <div className="chart-section">
              <div className="chart-header">
                <h3 className="chart-title">Monthly ROI Payouts Over Time</h3>
                <div className="chart-filter">
                  <select
                    value={timeFilter}
                    onChange={(e) => setTimeFilter(e.target.value)}
                    className="filter-select"
                  >
                    {timeFilters.map((filter) => (
                      <option key={filter.id} value={filter.id}>
                        {filter.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="chart-container">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={chartData}
                    margin={{
                      top: 20,
                      right: isMobile ? 10 : 20,
                      left: isMobile ? 5 : 10,
                      bottom: 20,
                    }}
                  >
                    <CartesianGrid
                      strokeDasharray="none"
                      stroke="rgba(255, 255, 255, 0.1)"
                      horizontal={true}
                      vertical={false}
                    />
                    <XAxis
                      dataKey="month"
                      axisLine={false}
                      tickLine={false}
                      tick={{
                        fill: '#999999',
                        fontSize: isMobile ? 10 : 12,
                        fontFamily: 'Montserrat'
                      }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{
                        fill: '#999999',
                        fontSize: isMobile ? 10 : 12,
                        fontFamily: 'Montserrat'
                      }}
                      tickFormatter={formatYAxisTick}
                      domain={['dataMin', 'dataMax']}
                      width={isMobile ? 45 : 60}
                    />
                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke="#4AFF4A"
                      strokeWidth={isMobile ? 2 : 3}
                      dot={{
                        fill: '#4AFF4A',
                        stroke: '#262626',
                        strokeWidth: 2,
                        r: isMobile ? 3 : 4
                      }}
                      activeDot={{
                        r: isMobile ? 5 : 6,
                        fill: '#4AFF4A',
                        stroke: '#262626',
                        strokeWidth: 2
                      }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ROIDistribution;

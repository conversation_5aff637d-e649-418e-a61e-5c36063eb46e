import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import '../styles/components/VisualAnalytics.css';
import DashboardLayout from './DashboardLayout';

// Import icons
import overviewIcon from '../assets/overview.png';
import walletMoneyIcon from '../assets/wallet-money.png';
import arrow2Icon from '../assets/arrow-2.png';
import cardCoinIcon from '../assets/card-coin.png';
import moneysIcon from '../assets/moneys.png';
import percentageSquareIcon from '../assets/percentage-square.png';
import chartIcon from '../assets/chart.png';
import spanImage from '../assets/span.png';

interface VisualAnalyticsProps {}

const VisualAnalytics: React.FC<VisualAnalyticsProps> = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('visual-analytics');
  const [selectedTimeFilter, setSelectedTimeFilter] = useState('this-year');

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: overviewIcon },
    { id: 'live-reserve', label: 'Live Reserve', icon: walletMoneyIcon },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: arrow2Icon },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: cardCoinIcon },
    { id: 'startup-funding', label: 'Startup Funding', icon: moneysIcon },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: percentageSquareIcon },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: chartIcon, active: true }
  ];

  const timeFilters = [
    { id: 'this-year', label: 'This year' },
    { id: 'last-year', label: 'Last year' },
    { id: 'all-time', label: 'All time' }
  ];

  // Growth of Total Staked Over Time data
  const growthData = [
    { month: 'Jan', value: 0 },
    { month: 'Feb', value: 500000 },
    { month: 'Mar', value: 750000 },
    { month: 'Apr', value: 900000 },
    { month: 'May', value: 1200000 },
    { month: 'Jun', value: 1400000 },
    { month: 'Jul', value: 1600000 },
    { month: 'Aug', value: 1800000 }
  ];

  // ROI Payouts by Tier data
  const roiPayoutsData = [
    { tier: '$50 - $99', amount: 700000, label: '$50 - $99' },
    { tier: '$100 - $249', amount: 1200000, label: '$100 - $249' },
    { tier: '$250 - $499', amount: 1800000, label: '$250 - $499' },
    { tier: '$500 - $999', amount: 900000, label: '$500 - $999' },
    { tier: '$1,000 - $2,499', amount: 1400000, label: '$1,000 - $2,499' },
    { tier: '$2,500 - $4,999', amount: 2200000, label: '$2,500 - $4,999' },
    { tier: '$5,000 - $9,999', amount: 1600000, label: '$5,000 - $9,999' },
    { tier: '$10,000 - $19,999', amount: 1100000, label: '$10,000 - $19,999' },
    { tier: '$20,000+', amount: 1900000, label: '$20,000+' }
  ];

  // Token Reserve Distribution data
  const tokenDistributionData = [
    { name: 'BTC Holdings', value: 28, amount: '$91,000 BTC', color: '#F7931A' },
    { name: 'Solana Holdings', value: 53, amount: '$126,000 SOL', color: '#14F195' },
    { name: 'USDC Holdings', value: 8, amount: '$91,500 USDC', color: '#2775CA' },
    { name: 'ESVC Reserves', value: 11, amount: '$51,500 ESVC', color: '#BF4129' }
  ];

  const handleSidebarClick = (itemId: string) => {
    setActiveTab(itemId);

    switch (itemId) {
      case 'overview':
        navigate('/overview');
        break;
      case 'live-reserve':
        navigate('/live-reserve');
        break;
      case 'daily-transactions':
        navigate('/daily-transactions');
        break;
      case 'real-time-staking':
        navigate('/real-time-staking');
        break;
      case 'startup-funding':
        navigate('/startup-funding');
        break;
      case 'roi-distribution':
        navigate('/roi-distribution');
        break;
      case 'visual-analytics':
        // Already on this page
        break;
      default:
        console.log(`Loading ${itemId} page...`);
    }
  };

  const formatValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    }
    return `$${(value / 1000).toFixed(0)}K`;
  };

  const formatGrowthValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    }
    return `$${(value / 1000).toFixed(0)}K`;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip">
          <p className="tooltip-label">{`${label}`}</p>
          <p className="tooltip-value">
            {`${formatValue(payload[0].value)}`}
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomGrowthTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip">
          <p className="tooltip-label">{`${label}`}</p>
          <p className="tooltip-value">
            {`${formatGrowthValue(payload[0].value)}`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <DashboardLayout className="visual-analytics-container">
      <div className="visual-analytics-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <aside className="dashboard-sidebar">
            <nav className="sidebar-nav">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  className={`sidebar-item ${activeTab === item.id ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <img src={item.icon} alt={item.label} className="sidebar-icon" />
                  <span className="sidebar-label">{item.label}</span>
                </button>
              ))}
            </nav>
          </aside>

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="visual-analytics-header">
              <h2 className="section-title">Visual Analytics</h2>
            </div>

            {/* Charts Container */}
            <div className="charts-container">
              {/* Growth of Total Staked Over Time */}
              <div className="chart-card">
                <div className="chart-header">
                  <h3 className="chart-title">Growth of Total Staked Over Time</h3>
                  <div className="time-filter-dropdown">
                    <select
                      value={selectedTimeFilter}
                      onChange={(e) => setSelectedTimeFilter(e.target.value)}
                      className="filter-select"
                    >
                      {timeFilters.map((filter) => (
                        <option key={filter.id} value={filter.id}>
                          {filter.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="chart-wrapper">
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={growthData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#404040" />
                      <XAxis
                        dataKey="month"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#CCCCCC', fontSize: 12 }}
                      />
                      <YAxis
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#CCCCCC', fontSize: 12 }}
                        tickFormatter={formatGrowthValue}
                      />
                      <Tooltip content={<CustomGrowthTooltip />} />
                      <Line
                        type="monotone"
                        dataKey="value"
                        stroke="#14F195"
                        strokeWidth={3}
                        dot={{ fill: '#14F195', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#14F195', strokeWidth: 2 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* ROI Payouts by Tier */}
              <div className="chart-card">
                <div className="chart-header">
                  <h3 className="chart-title">ROI Payouts by Tier</h3>
                  <div className="time-filter-dropdown">
                    <select
                      value={selectedTimeFilter}
                      onChange={(e) => setSelectedTimeFilter(e.target.value)}
                      className="filter-select"
                    >
                      {timeFilters.map((filter) => (
                        <option key={filter.id} value={filter.id}>
                          {filter.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="chart-wrapper">
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={roiPayoutsData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#404040" />
                      <XAxis
                        dataKey="label"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#CCCCCC', fontSize: 10 }}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#CCCCCC', fontSize: 12 }}
                        tickFormatter={formatValue}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Bar
                        dataKey="amount"
                        fill="#D19049"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Token Reserve Distribution */}
              <div className="chart-card">
                <div className="chart-header">
                  <h3 className="chart-title">Token Reserve Distribution</h3>
                </div>
                <div className="chart-wrapper pie-chart-wrapper">
                  <div className="pie-chart-container">
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={tokenDistributionData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={120}
                          paddingAngle={2}
                          dataKey="value"
                        >
                          {tokenDistributionData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value: any, name: any, props: any) => [
                            `${value}%`,
                            props.payload.amount
                          ]}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                    <div className="pie-chart-center">
                      <div className="total-holdings">
                        <span className="total-label">Total Holdings</span>
                        <span className="total-value">$318,000.00</span>
                      </div>
                    </div>
                  </div>
                  <div className="pie-chart-legend">
                    {tokenDistributionData.map((item, index) => (
                      <div key={index} className="legend-item">
                        <div
                          className="legend-color"
                          style={{ backgroundColor: item.color }}
                        ></div>
                        <div className="legend-text">
                          <span className="legend-name">{item.name} ({item.value}%)</span>
                          <span className="legend-amount">{item.amount}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default VisualAnalytics;

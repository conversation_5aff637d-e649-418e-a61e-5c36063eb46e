import React, { useState } from 'react';
import '../styles/components/StartupFunding.css';
import DashboardLayout from './DashboardLayout';
import TreasuryDashboardSidebar from './TreasuryDashboardSidebar';
import TreasuryDashboardHeader from './TreasuryDashboardHeader';

// Import icons
import informationCircleIcon from '../assets/information-circle.png';
import trendUpIcon from '../assets/trend-up.png';

interface StartupFundingProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const StartupFunding: React.FC<StartupFundingProps> = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [regionFilter, setRegionFilter] = useState('all-regions');
  const [sortFilter, setSortFilter] = useState('most-recent');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const regionFilters = [
    { id: 'all-regions', label: 'All regions' },
    { id: 'usa', label: 'USA' },
    { id: 'kenya', label: 'Kenya' },
    { id: 'nigeria', label: 'Nigeria' }
  ];

  const sortFilters = [
    { id: 'most-recent', label: 'From most recent' },
    { id: 'oldest', label: 'From oldest' },
    { id: 'highest-amount', label: 'Highest amount' },
    { id: 'lowest-amount', label: 'Lowest amount' }
  ];

  // Mock startup funding data
  const startupData = Array.from({ length: 50 }, (_, index) => {
    const startupNames = ['Grandex Agrotech', 'TechFlow Solutions', 'GreenEnergy Corp', 'DataMind AI', 'HealthTech Plus'];
    const stages = ['MVP', 'Launched', 'Seed', 'Series A'];
    const amounts = [5000, 8000, 10000, 15000, 20000];
    const regions = ['Kenya', 'USA', 'Nigeria', 'Ghana'];
    
    return {
      id: index + 1,
      name: startupNames[index % startupNames.length],
      stage: stages[index % stages.length],
      amount: amounts[index % amounts.length],
      date: 'Jan, 5, 2025',
      region: regions[index % regions.length]
    };
  });

  const totalStartups = '11,302';
  const totalAmount = '91,000';
  const availableFunds = '$43,700';
  const changePercent = '+4.8% Today';



  const totalPages = Math.ceil(startupData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentStartupData = startupData.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    const centerPages = [];
    const maxVisiblePages = 5;

    // Page numbers for center
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      centerPages.push(
        <button
          key={i}
          className={`pagination-btn ${currentPage === i ? 'active' : ''}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }

    // Add ellipsis if needed
    if (startPage > 1) {
      centerPages.unshift(
        <span key="start-ellipsis" className="pagination-ellipsis">...</span>
      );
    }
    if (endPage < totalPages) {
      centerPages.push(
        <span key="end-ellipsis" className="pagination-ellipsis">...</span>
      );
    }

    return (
      <>
        {/* Previous button */}
        <button
          className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`}
          onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>

        {/* Center page numbers */}
        <div className="pagination-center">
          {centerPages}
        </div>

        {/* Next button */}
        <button
          className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`}
          onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next →
        </button>
      </>
    );
  };

  return (
    <DashboardLayout className="startup-funding-container">
      <div className="startup-funding-content">
        {/* Page Title */}
        <TreasuryDashboardHeader />

        <div className="dashboard-layout">
          {/* Sidebar */}
          <TreasuryDashboardSidebar />

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="funding-header">
              <h2 className="section-title">Startup Funding</h2>
            </div>

            {/* Stats Cards */}
            <div className="funding-stats">
              <div className="stats-card">
                <div className="stats-label">TOTAL STARTUPS FUNDED</div>
                <div className="stats-value">{totalStartups}</div>
                <div className="stats-sublabel">TOTAL FUNDED AMOUNT</div>
                <div className="stats-subvalue">{totalAmount}</div>
              </div>

              <div className="stats-card funds-card">
                <div className="stats-label">
                  FUNDS AVAILABLE TO FUND STARTUPS
                  <div className="tooltip-container">
                    <img src={informationCircleIcon} alt="Info" className="info-icon" />
                    <div className="tooltip">
                      We dedicate 5% of our total profits to backing startups, including ideas pitched by stakers like you.
                    </div>
                  </div>
                </div>
                <div className="stats-value">{availableFunds}</div>
                <div className="stats-change">
                  <img src={trendUpIcon} alt="Trend up" className="trend-icon" />
                  {changePercent}
                </div>
              </div>
            </div>

            {/* Funding History */}
            <div className="funding-history">
              {/* Filters */}
              <div className="funding-filters">
                <div className="search-filter">
                  <input
                    type="text"
                    placeholder="Search startup name"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="search-input"
                  />
                </div>
                
                <div className="dropdown-filters">
                  <select
                    value={regionFilter}
                    onChange={(e) => setRegionFilter(e.target.value)}
                    className="filter-select"
                  >
                    {regionFilters.map((filter) => (
                      <option key={filter.id} value={filter.id}>
                        {filter.label}
                      </option>
                    ))}
                  </select>

                  <select
                    value={sortFilter}
                    onChange={(e) => setSortFilter(e.target.value)}
                    className="filter-select"
                  >
                    {sortFilters.map((filter) => (
                      <option key={filter.id} value={filter.id}>
                        {filter.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Table */}
              <div className="funding-table">
                <div className="table-header">
                  <div className="table-cell">Startup Name</div>
                  <div className="table-cell">Stage</div>
                  <div className="table-cell">Funding Amount</div>
                  <div className="table-cell">Funding Date</div>
                  <div className="table-cell">Region</div>
                </div>

                {currentStartupData.map((startup, index) => (
                  <div key={startup.id} className="table-row">
                    <div className="table-cell">{startIndex + index + 1}. {startup.name}</div>
                    <div className="table-cell" data-label="Stage">
                      <span className={`stage-badge ${startup.stage.toLowerCase().replace(' ', '-')}`}>
                        {startup.stage}
                      </span>
                    </div>
                    <div className="table-cell" data-label="Funding Amount">${startup.amount.toLocaleString()}</div>
                    <div className="table-cell" data-label="Funding Date">{startup.date}</div>
                    <div className="table-cell" data-label="Region">{startup.region}</div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              <div className="pagination">
                {renderPagination()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default StartupFunding;

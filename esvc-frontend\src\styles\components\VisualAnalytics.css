/* Visual Analytics Container */
.visual-analytics-container {
  min-height: 100vh;
}

.visual-analytics-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 2;
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: 48px;
}

.page-title {
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.title-with-span {
  position: relative;
  display: inline-block;
}

.title-span {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  font-weight: 400;
  color: #CCCCCC;
  margin: 0;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

/* Sidebar */
.dashboard-sidebar {
  width: 240px;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  height: fit-content;
  position: sticky;
  top: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  position: relative;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.sidebar-item.active {
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.3);
}

.sidebar-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  filter: brightness(0.8);
  transition: filter 0.3s ease;
}

.sidebar-item:hover .sidebar-icon,
.sidebar-item.active .sidebar-icon {
  filter: brightness(1);
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Header */
.visual-analytics-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/* Charts Container */
.charts-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Chart Card */
.chart-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.chart-title {
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.time-filter-dropdown {
  position: relative;
}

.filter-select {
  background: rgba(64, 64, 64, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  cursor: pointer;
  outline: none;
  transition: all 0.3s ease;
}

.filter-select:hover {
  background: rgba(64, 64, 64, 1);
  border-color: rgba(255, 255, 255, 0.2);
}

.filter-select option {
  background: #404040;
  color: #FFFFFF;
}

/* Chart Wrapper */
.chart-wrapper {
  width: 100%;
}

/* Custom Tooltip */
.custom-tooltip {
  background: rgba(26, 26, 26, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.tooltip-label {
  color: #CCCCCC;
  font-size: 12px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.tooltip-value {
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

/* Pie Chart Specific Styles */
.pie-chart-wrapper {
  display: flex;
  gap: 32px;
  align-items: center;
}

.pie-chart-container {
  position: relative;
  flex: 1;
}

.pie-chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.total-holdings {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.total-label {
  font-size: 12px;
  font-weight: 500;
  color: #CCCCCC;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.total-value {
  font-size: 18px;
  font-weight: 700;
  color: #FFFFFF;
}

.pie-chart-legend {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 200px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.legend-name {
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
}

.legend-amount {
  font-size: 12px;
  font-weight: 400;
  color: #CCCCCC;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .visual-analytics-content {
    padding: 0 16px;
  }

  .page-title {
    font-size: 32px;
  }

  .title-span {
    width: 80px;
    bottom: -6px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .dashboard-layout {
    flex-direction: column;
    gap: 24px;
  }

  .dashboard-sidebar {
    width: 100%;
    position: static;
    order: 1;
    padding: 12px;
    overflow-x: auto;
  }

  .sidebar-nav {
    flex-direction: row;
    gap: 8px;
    padding: 0 4px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .sidebar-nav::-webkit-scrollbar {
    display: none;
  }

  .sidebar-item {
    white-space: nowrap;
    min-width: 120px;
    max-width: 160px;
    padding: 0 14px;
    flex-shrink: 0;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.2;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-sizing: border-box;
  }

  .sidebar-icon {
    width: 16px;
    height: 16px;
  }

  .dashboard-content {
    order: 2;
  }

  .chart-card {
    padding: 16px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .pie-chart-wrapper {
    flex-direction: column;
    gap: 24px;
  }

  .pie-chart-legend {
    min-width: auto;
    width: 100%;
  }

  .total-value {
    font-size: 16px;
  }
}

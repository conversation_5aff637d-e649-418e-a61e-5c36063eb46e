import React from 'react';
import spanImage from '../assets/span.png';

interface TreasuryDashboardHeaderProps {
  className?: string;
}

const TreasuryDashboardHeader: React.FC<TreasuryDashboardHeaderProps> = ({ className = '' }) => {
  return (
    <div className={`page-header ${className}`}>
      <h1 className="page-title">
        <div className="title-with-span">
          Treasury
          <img src={spanImage} alt="Decorative span" className="title-span" />
        </div>
        Dashboard
      </h1>
      <p className="page-subtitle">
        Live insights into how funds are held, ROI is distributed, and startups are supported.
        Empowering you to stake with trust.
      </p>
    </div>
  );
};

export default TreasuryDashboardHeader;
